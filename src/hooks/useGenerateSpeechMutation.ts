import { useMutation } from '@tanstack/react-query'

export interface GenerateSpeechParams {
  voice_id: string
  text: string
  model_id?: string
  userId?: string
  projectId?: string // Add projectId for tracking
}

export interface GenerateSpeechResult {
  audioUrl: string // data:audio/mp3;base64,...
  alignment: {
    characters: string[]
    character_start_times_seconds: number[]
    character_end_times_seconds: number[]
  } // alignment object from API with separate arrays
}

export function useGenerateSpeechMutation() {
  return useMutation<GenerateSpeechResult, Error, GenerateSpeechParams>({
    mutationFn: async ({ voice_id, text, model_id, userId, projectId }) => {
      const response = await fetch('/api/elevenlabs/generate-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ voice_id, text, model_id, userId }),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Failed to generate speech: ${error}`)
      }

      const result = await response.json()

      // Voice regeneration tracking is now handled in useGatedVoiceGeneration hook
      // No need to track here since it's handled at a higher level

      return result
    },
  })
}
