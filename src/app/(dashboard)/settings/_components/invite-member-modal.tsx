'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { UserPlus, Loader2, Gem } from 'lucide-react'
import { toast } from 'sonner'
import { useTeamMemberAccess } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'

interface InviteMemberModalProps {
  onInvite: (email: string, role: string) => Promise<void>
  loading?: boolean
  disabled?: boolean
}

export function InviteMemberModal({
  onInvite,
  loading = false,
  disabled = false,
}: InviteMemberModalProps) {
  const [open, setOpen] = useState(false)
  const [email, setEmail] = useState('')
  const [role, setRole] = useState('member')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const teamMemberAccess = useTeamMemberAccess()
  const { openUpgradeModal } = useUpgradeModal()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email.trim()) return

    // Check team member limits
    if (!teamMemberAccess.allowed) {
      openUpgradeModal('teamMembers', teamMemberAccess.upgradeMessage)
      return
    }

    try {
      setIsSubmitting(true)
      await onInvite(email.trim(), role)
      setEmail('')
      setRole('member')
      setOpen(false)
      toast.success(`Invitation sent to ${email.trim()}`)
    } catch (error) {
      console.error('Failed to invite member:', error)
      toast.error('Failed to send invitation. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleOpenModal = () => {
    if (!teamMemberAccess.allowed) {
      openUpgradeModal('teamMembers', teamMemberAccess.upgradeMessage)
      return
    }
    setOpen(true)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!disabled && !loading) {
      setOpen(newOpen)
      if (!newOpen) {
        // Reset form when closing
        setEmail('')
        setRole('member')
        setIsSubmitting(false)
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <Button
        onClick={handleOpenModal}
        className={`transition-colors duration-200 ${
          !teamMemberAccess.allowed
            ? 'bg-gradient-to-r from-orange-600 to-orange-500 text-white hover:from-orange-700 hover:to-orange-600'
            : 'bg-primary text-primary-foreground hover:bg-primary/90'
        }`}
        disabled={disabled || loading}
      >
        {!teamMemberAccess.allowed ? (
          <Gem className='h-4 w-4 mr-2' />
        ) : (
          <UserPlus className='h-4 w-4 mr-2' />
        )}
        {!teamMemberAccess.allowed
          ? 'Upgrade to Invite Members'
          : 'Invite Member'}
      </Button>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <UserPlus className='h-5 w-5' />
            Invite Team Member
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='email'>Email Address</Label>
            <Input
              id='email'
              type='email'
              placeholder='Enter email address'
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
              disabled={isSubmitting || loading}
              className='transition-colors duration-200'
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='role'>Role</Label>
            <Select
              value={role}
              onValueChange={setRole}
              disabled={isSubmitting || loading}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select role' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='member'>Member</SelectItem>
                <SelectItem value='admin'>Admin</SelectItem>
                <SelectItem value='owner'>Owner</SelectItem>
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>
              Members can view and edit content. Admins can manage team members.
              Owners have full control.
            </p>
          </div>
          <div className='flex justify-end space-x-2 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={() => setOpen(false)}
              disabled={isSubmitting || loading}
              className='transition-colors duration-200'
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={!email.trim() || isSubmitting || loading}
              className='bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200'
            >
              {isSubmitting ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  Sending...
                </>
              ) : (
                'Send Invitation'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
