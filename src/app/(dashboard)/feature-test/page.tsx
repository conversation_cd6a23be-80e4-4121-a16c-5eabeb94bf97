'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  useFeatureAccess, 
  useCanCreateProject, 
  useVideoExportAccess,
  useAIImageLimits,
  useVideoPublishingAccess,
  useTeamMemberAccess,
  useVideoDurationLimits,
  usePlanLimits
} from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import { FeatureGate, GatedButton, UsageBadge } from '@/components/feature-gate'
import { PremiumBadge } from '@/components/ui/premium-badge'

export default function FeatureTestPage() {
  const { openUpgradeModal } = useUpgradeModal()
  const { planName, limits } = usePlanLimits()
  
  // Test all feature access hooks
  const projectAccess = useCanCreateProject()
  const exportAccess = useVideoExportAccess()
  const aiImageAccess = useAIImageLimits()
  const publishingAccess = useVideoPublishingAccess()
  const teamAccess = useTeamMemberAccess()
  const durationLimits = useVideoDurationLimits()

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Feature Gating Test Page</h1>
        <Badge variant="outline" className="text-lg px-3 py-1">
          Current Plan: {planName.charAt(0).toUpperCase() + planName.slice(1)}
        </Badge>
      </div>

      {/* Plan Limits Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Plan Limits Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{limits?.projects || 0}</div>
              <div className="text-sm text-muted-foreground">Projects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{limits?.videoExports || 0}</div>
              <div className="text-sm text-muted-foreground">Video Exports</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{limits?.aiImages || 0}</div>
              <div className="text-sm text-muted-foreground">AI Images</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{Math.floor((limits?.videoDuration || 60) / 60)}min</div>
              <div className="text-sm text-muted-foreground">Video Duration</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Access Tests */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        {/* Project Creation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Project Creation
              <UsageBadge feature="projects" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Can create: {projectAccess.canCreate ? '✅ Yes' : '❌ No'}
            </div>
            <div className="text-sm">
              Used: {projectAccess.current}/{projectAccess.limit}
            </div>
            <GatedButton
              feature="projects"
              onClick={() => console.log('Creating project...')}
              premiumText="Upgrade to Create More Projects"
            >
              Create New Project
            </GatedButton>
          </CardContent>
        </Card>

        {/* Video Export */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Video Export
              <UsageBadge feature="videoExports" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Can export: {exportAccess.allowed ? '✅ Yes' : '❌ No'}
            </div>
            <div className="text-sm">
              Used: {exportAccess.current}/{exportAccess.limit}
            </div>
            <GatedButton
              feature="videoExports"
              onClick={() => console.log('Exporting video...')}
              premiumText="Upgrade to Export Videos"
            >
              Export Video
            </GatedButton>
          </CardContent>
        </Card>

        {/* AI Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              AI Image Generation
              <UsageBadge feature="aiImages" showPercentage />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Can generate: {aiImageAccess.allowed ? '✅ Yes' : '❌ No'}
            </div>
            <div className="text-sm">
              Remaining: {aiImageAccess.remaining}/{aiImageAccess.limit}
            </div>
            <GatedButton
              feature="aiImages"
              onClick={() => console.log('Generating AI image...')}
              premiumText="Upgrade for More AI Images"
            >
              Generate AI Image
            </GatedButton>
          </CardContent>
        </Card>

        {/* Video Publishing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Video Publishing
              {!publishingAccess.allowed && <PremiumBadge variant="crown" />}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Can publish: {publishingAccess.allowed ? '✅ Yes' : '❌ No'}
            </div>
            <FeatureGate 
              feature="videoPublishing"
              fallback={
                <div className="text-sm text-orange-600">
                  Video publishing is only available on Premium plan
                </div>
              }
            >
              <Button onClick={() => console.log('Publishing to YouTube...')}>
                Publish to YouTube
              </Button>
            </FeatureGate>
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Team Members
              <UsageBadge feature="teamMembers" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Can invite: {teamAccess.allowed ? '✅ Yes' : '❌ No'}
            </div>
            <div className="text-sm">
              Remaining: {teamAccess.remaining}/{teamAccess.limit}
            </div>
            <GatedButton
              feature="teamMembers"
              onClick={() => console.log('Inviting team member...')}
              premiumText="Upgrade to Invite More Members"
            >
              Invite Team Member
            </GatedButton>
          </CardContent>
        </Card>

        {/* Video Duration */}
        <Card>
          <CardHeader>
            <CardTitle>Video Duration Limits</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Max duration: {Math.floor(durationLimits.maxDuration / 60)} minute(s)
            </div>
            <div className="text-sm">
              Plan: {durationLimits.planName}
            </div>
            <Button 
              onClick={() => openUpgradeModal('videoDuration', durationLimits.upgradeMessage)}
              variant="outline"
            >
              Test Duration Upgrade Modal
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Test Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Test Buttons</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={() => openUpgradeModal('projects', 'Test project upgrade message')}
              variant="outline"
            >
              Test Project Upgrade Modal
            </Button>
            <Button 
              onClick={() => openUpgradeModal('videoExports', 'Test export upgrade message')}
              variant="outline"
            >
              Test Export Upgrade Modal
            </Button>
            <Button 
              onClick={() => openUpgradeModal('aiImages', 'Test AI image upgrade message')}
              variant="outline"
            >
              Test AI Image Upgrade Modal
            </Button>
            <Button 
              onClick={() => openUpgradeModal('videoPublishing', 'Test publishing upgrade message')}
              variant="outline"
            >
              Test Publishing Upgrade Modal
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
